{
    "pages": [
        {
            "path": "pages/index/index"
        }
    ],
    "subPackages": [
        {
            "root": "pages/create-activity",
            "pages": [
                {
                    "path": "index",
                    "style": {
                        "navigationBarTitleText": "创建活动"
                    }
                }
            ]
        },
        {
            "root": "pages/diy-page",
            "pages": [
                {
                    "path": "diy-page"
                },
                {
                    "path": "diy-index"
                }
            ]
        },
        {
            "root": "pages/ranking-list",
            "pages": [
                {
                    "path": "ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/user",
            "pages": [
                {
                    "path": "user",
                    "style": {
                        "navigationBarTitleText": "个人中心",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "user_detail",
                    "style": {
                        "navigationBarTitleText": "个人资料",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "integral_record",
                    "style": {
                        "navigationBarTitleText": "我的积分",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/activity",
            "pages": [
                {
                    "path": "user/index",
                    "style": {
                        "navigationBarTitleText": "线上云接力",
                        "navigationBarBackgroundColor": "#72c3fb",
                        "navigationBarTextStyle": "white",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "user/detail",
                    "style": {
                        "navigationBarTitleText": "活动详情",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "user/road-map/road_map",
                    "style": {
                        "navigationBarTitleText": "路线图",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "user/activity_list",
                    "style": {
                        "navigationBarTitleText": "活动列表",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "user/ranking_list",
                    "style": {
                        "navigationBarTitleText": "排行榜",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "admin/add_or_edit",
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/team_list",
                    "style": {
                        "navigationBarTitleText": "队伍管理",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "admin/add_edit_team"
                },
                {
                    "path": "admin/add_or_edit_point",
                    "style": {
                        "navigationBarTitleText": "添加点位",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/point_list",
                    "style": {
                        "navigationBarTitleText": "点位管理",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "admin/activity_user_list",
                    "style": {
                        "navigationBarTitleText": "活动用户",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "admin/blacklist",
                    "style": {
                        "navigationBarTitleText": "黑名单用户"
                    }
                },
                {
                    "path": "admin/export_step",
                    "style": {
                        "navigationBarTitleText": "数据导出",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/export_record",
                    "style": {
                        "navigationBarTitleText": "数据导出记录",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/medal/list",
                    "style": {
                        "navigationBarTitleText": "勋章列表",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/medal/add"
                },
                {
                    "path": "admin/export_total_step",
                    "style": {
                        "navigationBarTitleText": "数据导出",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/export_rank",
                    "style": {
                        "navigationBarTitleText": "导出排行榜数据"
                    }
                },
                {
                    "path": "admin/add_teams",
                    "style": {
                        "navigationBarTitleText": "批量创建队伍"
                    }
                },
                {
                    "path": "admin/detail-page-template/list",
                    "style": {
                        "navigationBarTitleText": "模版市场"
                    }
                },
                {
                    "path": "admin/detail-page-template/details"
                },
                {
                    "path": "admin/reward-team-person/reward-record",
                    "style": {
                        "navigationBarTitleText": "红包记录"
                    }
                },
                {
                    "path": "admin/export-activity-total-ranking",
                    "style": {
                        "navigationBarTitleText": "导出活跃度排行榜"
                    }
                },
                {
                    "path": "admin/export-real-step-ranking",
                    "style": {
                        "navigationBarTitleText": "导出实际步数排行榜"
                    }
                },
                {
                    "path": "other/point_step",
                    "style": {
                        "navigationBarTitleText": "地图点位",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "other/certificate",
                    "style": {
                        "navigationBarTitleText": "活动证书",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "other/user_exchange_list"
                },
                {
                    "path": "other/answer/list",
                    "style": {
                        "navigationBarTitleText": "答题"
                    }
                },
                {
                    "path": "other/answer/record_list",
                    "style": {
                        "navigationBarTitleText": "答题记录"
                    }
                },
                {
                    "path": "other/activity_active_list",
                    "style": {
                        "navigationBarTitleText": "平台活动活跃度排行",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "other/medal_list",
                    "style": {
                        "navigationBarTitleText": "勋章墙",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "other/bullet_screen",
                    "style": {
                        "navigationBarTitleText": "活动弹幕",
                        "navigationBarBackgroundColor": "#ffffff",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "other/every-day-certificate",
                    "style": {
                        "navigationBarTitleText": "列表"
                    }
                },
                {
                    "path": "feedback/send",
                    "style": {
                        "navigationBarTitleText": "意见反馈",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "feedback/list",
                    "style": {
                        "navigationBarTitleText": "用户留言反馈",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "feedback/detail",
                    "style": {
                        "navigationBarTitleText": "留言反馈详情",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/import_users/user_list",
                    "style": {
                        "navigationBarTitleText": "已导入用户名单列表"
                    }
                },
                {
                    "path": "business-kilometers/user-list",
                    "style": {
                        "navigationBarTitleText": "出差里程兑换用户名单"
                    }
                },
                {
                    "path": "business-kilometers/kilometer-exchange",
                    "style": {
                        "navigationBarTitleText": "出差里程兑换"
                    }
                },
                {
                    "path": "together-pic/make-pictures",
                    "style": {
                        "navigationBarTitleText": "照片合成"
                    }
                },
                {
                    "path": "relay-race/sort-setting",
                    "style": {
                        "navigationBarTitleText": "接力顺序"
                    }
                },
                {
                    "path": "light-up-map/map",
                    "style": {
                        "navigationBarTitleText": "点亮地图"
                    }
                },
                {
                    "path": "other/max-step-submit-list",
                    "style": {
                        "navigationBarTitleText": "提交记录"
                    }
                },
                {
                    "path": "other/exam-prize",
                    "style": {
                        "navigationBarTitleText": "答题勋章"
                    }
                }
            ]
        },
        {
            "root": "pages/other",
            "pages": [
                {
                    "path": "image_upload_or_select",
                    "style": {
                        "navigationBarTitleText": "图片选择",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "contact",
                    "style": {
                        "navigationBarTitleText": "联系客服",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "webview",
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "friend_list",
                    "style": {
                        "navigationBarTitleText": "邀请好友",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "icon-list/icon-list",
                    "style": {
                        "navigationBarTitleText": "图标"
                    }
                },
                {
                    "path": "today_in_history",
                    "style": {
                        "navigationBarTitleText": "历史上的今天"
                    }
                },
                {
                    "path": "headimg-list/headimg-list",
                    "style": {
                        "navigationBarTitleText": "更换头像",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "batch_import/import-excel",
                    "style": {
                        "navigationBarTitleText": "导入活动人员名单"
                    }
                },
                {
                    "path": "batch_import/download-template",
                    "style": {
                        "navigationBarTitleText": "模版下载"
                    }
                },
                {
                    "path": "ai-face",
                    "style": {
                        "navigationBarTitleText": "AI换脸"
                    }
                },
                {
                    "path": "today-news",
                    "style": {
                        "navigationBarTitleText": "今日简讯"
                    }
                },
                {
                    "path": "activity-document",
                    "style": {
                        "navigationBarTitleText": "活动帮助文档"
                    }
                },
                {
                    "path": "wechat-message-push-set/set",
                    "style": {
                        "navigationBarTitleText": "消息推送设置"
                    }
                },
                {
                    "path": "h5-scan-code-login",
                    "style": {
                        "navigationBarTitleText": "扫码登录"
                    }
                },
                {
                    "path": "AI-analysis-report",
                    "style": {
                        "navigationBarTitleText": "AI分析报告"
                    }
                },
                {
                    "path": "small-feature-collection",
                    "style": {
                        "navigationBarTitleText": "日常记录"
                    }
                },
                {
                    "path": "my-blind-box-list"
                },
                {
                    "path": "lime-signature/lime-signature",
                    "style": {
                        "navigationBarTitleText": "签名"
                    }
                },
                {
                    "path": "word-remove-markdown",
                    "style": {
                        "navigationBarTitleText": "去除 Markdown"
                    }
                },
                {
                    "path": "export-activity-user-list",
                    "style": {
                        "navigationBarTitleText": "导出活动用户"
                    }
                },
                {
                    "path": "export-backend-ranking",
                    "style": {
                        "navigationBarTitleText": "导出排行榜"
                    }
                },
                {
                    "path": "export-day-integral-ranking",
                    "style": {
                        "navigationBarTitleText": "按日导出积分排行榜"
                    }
                },
                {
                    "path": "export-month-integral-ranking",
                    "style": {
                        "navigationBarTitleText": "按月导出积分排行榜"
                    }
                },
                {
                    "path": "export-exam-ranking",
                    "style": {
                        "navigationBarTitleText": "导出考卷排行榜"
                    }
                },
                {
                    "path": "user-activity-report/report",
                    "style": {
                        "navigationBarTitleText": "活动报告"
                    }
                },
                {
                    "path": "export-feedback",
                    "style": {
                        "navigationBarTitleText": "用户反馈导出"
                    }
                },
                {
                    "path": "export-ai-sport-integral-list",
                    "style": {
                        "navigationBarTitleText": "AI运动积分排行榜导出"
                    }
                },
                {
                    "path": "front-export-ranking",
                    "style": {
                        "navigationBarTitleText": "排行榜导出"
                    }
                }
            ]
        },
        {
            "root": "pages/category",
            "pages": [
                {
                    "path": "list"
                },
                {
                    "path": "edit"
                }
            ]
        },
        {
            "root": "pages/news",
            "pages": [
                {
                    "path": "edit",
                    "style": {
                        "navigationBarTitleText": "文章编辑",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "list",
                    "style": {
                        "navigationBarTitleText": "文章列表",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "preview",
                    "style": {
                        "navigationBarTitleText": "文章详情",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "study-news-exam/list",
                    "style": {
                        "navigationBarTitleText": "学习专区"
                    }
                }
            ]
        },
        // #ifdef H5
        {
            "root": "pages/H5",
            "pages": [
                {
                    "path": "login",
                    "style": {
                        "navigationBarTitleText": "登录",
                        "enablePullDownRefresh": false
                    }
                }
            ]
        },
        // #endif
        {
            "root": "pages/comment",
            "pages": [
                {
                    "path": "list",
                    "style": {
                        "navigationBarTitleText": "运动圈",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "publish",
                    "style": {
                        "navigationBarTitleText": "发布动态",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "detail",
                    "style": {
                        "navigationBarTitleText": "动态详情",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/idea_avatar",
            "pages": [
                {
                    "path": "pendant/pendant",
                    "style": {
                        "navigationBarTitleText": "头像挂件",
                        "navigationBarBackgroundColor": "#FE8C68",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "background/background",
                    "style": {
                        "navigationBarTitleText": "头像背景",
                        "navigationBarBackgroundColor": "#FE8C68",
                        "navigationBarTextStyle": "white"
                    }
                }
            ]
        },
        {
            "root": "pages/lottery",
            "pages": [
                {
                    "path": "admin/lottery/list",
                    "style": {
                        "navigationBarTitleText": "抽奖活动",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/lottery/edit"
                },
                {
                    "path": "admin/prize/list",
                    "style": {
                        "navigationBarTitleText": "奖品",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "admin/prize/edit",
                    "style": {
                        "navigationBarTitleText": "奖品"
                    }
                },
                {
                    "path": "user/lottery",
                    "style": {
                        "navigationBarTitleText": "抽奖",
                        "navigationBarBackgroundColor": "#d35443",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "user/lottery_record_list",
                    "style": {
                        "navigationBarTitleText": "抽奖记录"
                    }
                },
                {
                    "path": "user/step-lottery-list",
                    "style": {
                        "navigationBarTitleText": "抽奖"
                    }
                }
            ]
        },
        {
            "root": "pages/ai_sport",
            "pages": [
                {
                    "path": "pages/index",
                    "style": {
                        "navigationBarTitleText": "AI运动"
                    }
                },
                {
                    "path": "pages/manual-run",
                    "style": {
                        "navigationBarTitleText": "AI运动"
                    }
                },
                {
                    "path": "pages/ai-sport-recognition",
                    "style": {
                        "navigationBarTitleText": "AI运动识别"
                    }
                },
                {
                    "path": "pages/list",
                    "style": {
                        "navigationBarTitleText": "AI运动"
                    }
                },
                {
                    "path": "pages/record_list",
                    "style": {
                        "navigationBarTitleText": "运动记录"
                    }
                },
                {
                    "path": "pages/submit_success",
                    "style": {
                        "navigationBarTitleText": "提交成功"
                    }
                },
                {
                    "path": "pages/admin/add"
                },
                {
                    "path": "pages/admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "pages/user/detail",
                    "style": {
                        "navigationBarTitleText": "活动详情"
                    }
                },
                {
                    "path": "pages/user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜",
                        "enablePullDownRefresh": true
                    }
                }
            ],
            "plugins": {
                "aiSport": {
                    "version": "1.5.5",
                    "provider": "wx6130e578c4a26a1a"
                }
            }
        },
        {
            "root": "pages/shop",
            "pages": [
                {
                    "path": "goods/list",
                    "style": {
                        "navigationBarTitleText": "商品列表"
                    }
                },
                {
                    "path": "goods/detail",
                    "style": {
                        "navigationBarTitleText": "商品详情"
                    }
                },
                {
                    "path": "goods/cart",
                    "style": {
                        "navigationBarTitleText": "购物车"
                    }
                },
                {
                    "path": "order/pay_order",
                    "style": {
                        "navigationBarTitleText": "下单",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "order/order_list",
                    "style": {
                        "navigationBarTitleText": "订单列表",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "order/order_detail",
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "logistics-tracking/list",
                    "style": {
                        "navigationBarTitleText": "物流单号"
                    }
                }
            ]
        },
        {
            "root": "pages/voice",
            "pages": [
                {
                    "path": "admin/activity/add"
                },
                {
                    "path": "admin/activity/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/activity/export_rank",
                    "style": {
                        "navigationBarTitleText": "导出用户作品"
                    }
                },
                {
                    "path": "admin/background-music/list",
                    "style": {
                        "navigationBarTitleText": "背景音乐"
                    }
                },
                {
                    "path": "admin/background-music/add"
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/voice_list"
                },
                {
                    "path": "user/voice_detail"
                },
                {
                    "path": "user/article_list",
                    "style": {
                        "navigationBarTitleText": "录制"
                    }
                },
                {
                    "path": "user/recorder",
                    "style": {
                        "navigationBarTitleText": "开始录制"
                    }
                },
                {
                    "path": "user/submit_voice",
                    "style": {
                        "navigationBarTitleText": "提交"
                    }
                }
            ]
        },
        {
            "root": "pages/clock_in",
            "pages": [
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/point_list",
                    "style": {
                        "navigationBarTitleText": "打卡",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "user/point_detail"
                },
                {
                    "path": "user/sign_record_list",
                    "style": {
                        "navigationBarTitleText": "打卡记录"
                    }
                },
                {
                    "path": "user/sign_record_edit",
                    "style": {
                        "navigationBarTitleText": "打卡"
                    }
                },
                {
                    "path": "user/public_sign_list"
                },
                {
                    "path": "user/lottery-list",
                    "style": {
                        "navigationBarTitleText": "抽奖"
                    }
                },
                {
                    "path": "user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜"
                    }
                },
                {
                    "path": "user/signature",
                    "style": {
                        "navigationBarTitleText": "签名"
                    }
                },
                {
                    "path": "admin/activity/add"
                },
                {
                    "path": "admin/activity/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/activity/books_date_list",
                    "style": {
                        "navigationBarTitleText": "用户报名时间"
                    }
                },
                {
                    "path": "admin/point/list",
                    "style": {
                        "navigationBarTitleText": "点位管理"
                    }
                },
                {
                    "path": "admin/point/detail"
                },
                {
                    "path": "user/clock-in-images",
                    "style": {
                        "navigationBarTitleText": "打卡相册",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "admin/point/point-picture/list",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/sport_clock_in",
            "pages": [
                {
                    "path": "admin/activity/add"
                },
                {
                    "path": "admin/activity/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/sport_list",
                    "style": {
                        "navigationBarTitleText": "运动打卡"
                    }
                },
                {
                    "path": "user/public_sign_list",
                    "style": {
                        "navigationBarTitleText": "打卡广场"
                    }
                },
                {
                    "path": "user/sign",
                    "style": {
                        "navigationBarTitleText": "打卡"
                    }
                }
            ]
        },
        {
            "root": "pages/vote",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/add_candidate",
                    "style": {
                        "navigationBarTitleText": "添加候选人"
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/works_list"
                },
                {
                    "path": "user/works_detail",
                    "style": {
                        "navigationBarTitleText": "投票"
                    }
                },
                {
                    "path": "user/submit",
                    "style": {
                        "navigationBarTitleText": "提交参赛作品"
                    }
                },
                {
                    "path": "user/bulk-voting",
                    "style": {
                        "navigationBarTitleText": "投票"
                    }
                },
                {
                    "path": "user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/words",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/word_bind_answer_list",
                    "style": {
                        "navigationBarTitleText": "集字关卡管理"
                    }
                },
                {
                    "path": "admin/word_bind_answer",
                    "style": {
                        "navigationBarTitleText": "添加关卡"
                    }
                },
                {
                    "path": "admin/words-stock",
                    "style": {
                        "navigationBarTitleText": "库存",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/help",
                    "style": {
                        "navigationBarBackgroundColor": "#f44b44",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "user/share_word",
                    "style": {
                        "navigationBarBackgroundColor": "#c6131d",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "user/word_list",
                    "style": {
                        "navigationBarTitleText": "集字"
                    }
                },
                {
                    "path": "user/answer/list",
                    "style": {
                        "navigationBarTitleText": "答题"
                    }
                }
            ]
        },
        {
            "root": "pages/wallet",
            "pages": [
                {
                    "path": "redpack_list",
                    "style": {
                        "navigationBarTitleText": "红包记录"
                    }
                },
                {
                    "path": "export_rank",
                    "style": {
                        "navigationBarTitleText": "导出"
                    }
                },
                {
                    "path": "withdraw",
                    "style": {
                        "navigationBarTitleText": "提现"
                    }
                },
                {
                    "path": "widthdraw_record",
                    "style": {
                        "navigationBarTitleText": "提现记录"
                    }
                }
            ]
        },
        {
            "root": "pages/sign_in",
            "pages": [
                {
                    "path": "sign_in",
                    "style": {
                        "navigationBarTitleText": "签到"
                    }
                },
                {
                    "path": "record_list",
                    "style": {
                        "navigationBarTitleText": "签到记录"
                    }
                },
                {
                    "path": "integral_record_list"
                }
            ]
        },
        {
            "root": "pages/running",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/run",
                    "style": {
                        "navigationBarTitleText": "运动"
                    }
                },
                {
                    "path": "user/run_success",
                    "style": {
                        "navigationBarTitleText": "提交成功"
                    }
                },
                {
                    "path": "user/run_map",
                    "style": {
                        "navigationBarTitleText": "运动轨迹"
                    }
                },
                {
                    "path": "user/run_list",
                    "style": {
                        "navigationBarTitleText": "运动广场",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/cloud_wish",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/export-wish-list",
                    "style": {
                        "navigationBarTitleText": "祝福语导出"
                    }
                },
                {
                    "path": "user/detail",
                    "style": {
                        "navigationBarBackgroundColor": "#fef7eb"
                    }
                },
                {
                    "path": "user/wish_detail",
                    "style": {
                        "navigationBarTitleText": "祝福",
                        "navigationBarBackgroundColor": "#ee8262",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "wish_list"
                }
            ]
        },
        {
            "root": "pages/games",
            "pages": [
                {
                    "path": "games_center",
                    "style": {
                        "navigationBarTitleText": "游戏中心"
                    }
                },
                {
                    "path": "record_list"
                },
                {
                    "path": "2048/2048",
                    "style": {
                        "navigationBarTitleText": "2048",
                        "disableScroll": true
                    }
                },
                {
                    "path": "elsfk/elsfk",
                    "style": {
                        "navigationBarTitleText": "俄罗斯方块",
                        "navigationBarBackgroundColor": "#A1AA8D"
                    }
                },
                {
                    "path": "wzq/wzq",
                    "style": {
                        "navigationBarTitleText": "五子棋"
                    }
                },
                {
                    "path": "dds/dds",
                    "style": {
                        "navigationBarTitleText": "打地鼠",
                        "navigationBarBackgroundColor": "#45454d",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "gluttonous-snake/gluttonous-snake",
                    "style": {
                        "navigationBarTitleText": "贪吃蛇",
                        "disableScroll": true
                    }
                },
                {
                    "path": "idiom-solitaire/idiom-solitaire",
                    "style": {
                        "navigationBarTitleText": "成语接龙",
                        "disableScroll": true
                    }
                },
                {
                    "path": "garbage-sorting/garbage-sorting",
                    "style": {
                        "navigationBarTitleText": "垃圾分类",
                        "disableScroll": true
                    }
                },
                {
                    "path": "garbage-sorting/garbage-manage/list",
                    "style": {
                        "navigationBarTitleText": "垃圾管理",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "garbage-sorting/garbage-manage/add"
                },
                {
                    "path": "picture-composition",
                    "style": {
                        "navigationBarTitleText": "图片合成",
                        "disableScroll": true
                    }
                },
                {
                    "path": "one-stroke-line/game",
                    "style": {
                        "navigationBarTitleText": "一笔连线"
                    }
                },
                {
                    "path": "play-nian/play-nian",
                    "style": {
                        "navigationBarTitleText": "打年兽",
                        "disableScroll": true
                    }
                },
                {
                    "path": "aircraft-battle/aircraft-battle",
                    "style": {
                        "navigationBarTitleText": "飞机大战",
                        "navigationBarBackgroundColor": "#C3C8C9"
                    }
                },
                {
                    "path": "sheep-and-sheep/sheep-and-sheep",
                    "style": {
                        "navigationBarTitleText": "羊了个羊"
                    }
                },
                {
                    "path": "fan-fan-le/fan-fan-le",
                    "style": {
                        "navigationBarTitleText": "翻翻乐",
                        "navigationBarBackgroundColor": "#fa9990",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "counting-money/game",
                    "style": {
                        "navigationBarTitleText": "数钱",
                        "navigationBarBackgroundColor": "#FCEDB6",
                        "disableScroll": true
                    }
                },
                {
                    "path": "speech-recognition/game",
                    "style": {
                        "navigationBarTitleText": "绕口令"
                    }
                },
                {
                    "path": "penalty-kick/game",
                    "style": {
                        "navigationBarTitleText": "点球大战",
                        "disableScroll": true
                    }
                },
                {
                    "path": "pony-run-fast/game",
                    "style": {
                        "navigationBarTitleText": "小马快跑",
                        "disableScroll": true
                    }
                },
                {
                    "path": "eliminate-joy/game",
                    "style": {
                        "navigationBarTitleText": "消消乐",
                        "disableScroll": true
                    }
                },
                {
                    "path": "balloon/game",
                    "style": {
                        "navigationBarTitleText": "打爆气球",
                        "disableScroll": true
                    }
                },
                {
                    "path": "jump-upwards/game",
                    "style": {
                        "navigationBarTitleText": "向上跳一跳",
                        "disableScroll": true
                    }
                },
                {
                    "path": "roll-dice/game",
                    "style": {
                        "navigationBarTitleText": "摇骰子",
                        "disableScroll": true
                    }
                }
            ]
        },
        {
            "root": "pages/song",
            "pages": [
                {
                    "path": "admin/activity/add"
                },
                {
                    "path": "admin/activity/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/song/list",
                    "style": {
                        "navigationBarTitleText": "歌曲列表"
                    }
                },
                {
                    "path": "admin/song/add",
                    "style": {
                        "navigationBarTitleText": "添加歌曲"
                    }
                },
                {
                    "path": "user/detail",
                    "style": {
                        "navigationBarTitleText": "活动详情"
                    }
                },
                {
                    "path": "user/guessing-songs",
                    "style": {
                        "navigationBarTitleText": "猜歌",
                        "navigationBarBackgroundColor": "#B7C9FB",
                        "navigationBarTextStyle": "white"
                    }
                }
            ]
        },
        {
            "root": "pages/voice-red-pack",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/export-record",
                    "style": {
                        "navigationBarTitleText": "导出红包记录"
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/text-list",
                    "style": {
                        "navigationBarTitleText": "朗读内容"
                    }
                },
                {
                    "path": "user/recording",
                    "style": {
                        "navigationBarTitleText": "领红包",
                        "navigationBarBackgroundColor": "#E56755",
                        "navigationBarTextStyle": "white",
                        "disableScroll": true
                    }
                },
                {
                    "path": "user/red-pack-record",
                    "style": {
                        "navigationBarTitleText": "红包记录"
                    }
                }
            ]
        },
        {
            "root": "pages/registration",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/project/list",
                    "style": {
                        "navigationBarTitleText": "报名项目"
                    }
                },
                {
                    "path": "admin/registration-record",
                    "style": {
                        "navigationBarTitleText": "报名付费记录"
                    }
                },
                {
                    "path": "admin/project/add"
                },
                {
                    "path": "user/detail"
                }
            ]
        },
        {
            "root": "pages/guess-picture",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage"
                },
                {
                    "path": "user/detail"
                }
            ]
        },
        {
            "root": "pages/treasure-hunt",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/level-list",
                    "style": {
                        "navigationBarTitleText": "关卡"
                    }
                },
                {
                    "path": "user/look-for",
                    "style": {
                        "navigationStyle": "custom",
                        "pageOrientation": "landscape"
                    }
                }
            ]
        },
        {
            "root": "pages/card-collecting",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/point/list",
                    "style": {
                        "navigationBarTitleText": "卡片管理"
                    }
                },
                {
                    "path": "admin/point/set",
                    "style": {
                        "navigationBarTitleText": "卡片设置"
                    }
                },
                {
                    "path": "admin/point/image-position-set",
                    "style": {
                        "navigationBarTitleText": "卡片位置设置"
                    }
                },
                {
                    "path": "user/map",
                    "style": {
                        "navigationBarTitleText": "集卡"
                    }
                }
            ]
        },
        {
            "root": "pages/text-stitching",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/topic/list"
                },
                {
                    "path": "admin/topic/add"
                },
                {
                    "path": "user/detail"
                },
                {
                    "path": "user/stitching"
                }
            ]
        },
        {
            "root": "pages/task-challenge",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/level/list",
                    "style": {
                        "navigationBarTitleText": "关卡管理"
                    }
                },
                {
                    "path": "admin/level/add",
                    "style": {
                        "navigationBarTitleText": "添加关卡"
                    }
                },
                {
                    "path": "admin/export-other-ranking",
                    "style": {
                        "navigationBarTitleText": "排行榜导出"
                    }
                },
                {
                    "path": "user/details",
                    "style": {
                        "navigationBarTitleText": "活动详情"
                    }
                },
                {
                    "path": "user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜"
                    }
                },
                {
                    "path": "user/task-list",
                    "style": {
                        "navigationBarTitleText": "任务"
                    }
                },
                {
                    "path": "admin/export-integral-record",
                    "style": {
                        "navigationBarTitleText": "导出积分记录"
                    }
                },
                {
                    "path": "admin/export-step-ranking",
                    "style": {
                        "navigationBarTitleText": "导出步数排行榜"
                    }
                },
                {
                    "path": "admin/export-week-sport-user-list",
                    "style": {
                        "navigationBarTitleText": "导出"
                    }
                },
                {
                    "path": "admin/body-data-record",
                    "style": {
                        "navigationBarTitleText": "身体数据"
                    }
                },
                {
                    "path": "user/medal3-list",
                    "style": {
                        "navigationBarTitleText": "我的勋章",
                        "enablePullDownRefresh": false,
                        "navigationBarBackgroundColor": "#f8f8f8",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "admin/export-medal-count",
                    "style": {
                        "navigationBarTitleText": "导出勋章数",
                        "enablePullDownRefresh": false
                    }
                }
            ]
        },
        {
            "root": "pages/match-score",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/team/list",
                    "style": {
                        "navigationBarTitleText": "队伍管理"
                    }
                },
                {
                    "path": "admin/team/add",
                    "style": {
                        "navigationBarTitleText": "添加队伍"
                    }
                },
                {
                    "path": "user/details"
                },
                {
                    "path": "live-game/live-game",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "schedule/list",
                    "style": {
                        "navigationBarTitleText": "赛程",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/activity-program",
            "pages": [
                {
                    "path": "user/history",
                    "style": {
                        "navigationBarTitleText": "我的活动方案",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "user/generate",
                    "style": {
                        "navigationBarTitleText": "生成活动方案"
                    }
                },
                {
                    "path": "user/details",
                    "style": {
                        "navigationBarTitleText": "活动方案"
                    }
                }
            ]
        },
        {
            "root": "pages/wechat-step",
            "pages": [
                {
                    "path": "wechat-step-page",
                    "style": {
                        "navigationBarTitleText": "今日步数",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "history_step",
                    "style": {
                        "navigationBarTitleText": "月度步数",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "year_step",
                    "style": {
                        "navigationBarTitleText": "年度步数",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "history-step-list",
                    "style": {
                        "navigationBarTitleText": "历史步数"
                    }
                },
                {
                    "path": "sports-poster/sports-poster",
                    "style": {
                        "navigationBarTitleText": "运动海报"
                    }
                },
                {
                    "path": "sports-poster/background-list",
                    "style": {
                        "navigationBarTitleText": "更换图片"
                    }
                },
                {
                    "path": "sports-poster/words-list",
                    "style": {
                        "navigationBarTitleText": "更换短语"
                    }
                }
            ]
        },
        {
            "root": "pages/long-march-game",
            "pages": [
                {
                    "path": "admin/add",
                    "style": {
                        "navigationBarTitleText": "创建活动"
                    }
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "admin/levels/list",
                    "style": {
                        "navigationBarTitleText": "关卡管理"
                    }
                },
                {
                    "path": "admin/levels/add",
                    "style": {
                        "navigationBarTitleText": "添加关卡"
                    }
                },
                {
                    "path": "admin/scenes/list",
                    "style": {
                        "navigationBarTitleText": "场景管理"
                    }
                },
                {
                    "path": "admin/scenes/add",
                    "style": {
                        "navigationBarTitleText": "添加场景"
                    }
                },
                {
                    "path": "admin/options/list",
                    "style": {
                        "navigationBarTitleText": "选项管理"
                    }
                },
                {
                    "path": "admin/options/add",
                    "style": {
                        "navigationBarTitleText": "添加选项"
                    }
                },
                {
                    "path": "user/details",
                    "style": {
                        "navigationBarTitleText": "活动详情"
                    }
                },
                {
                    "path": "user/game/game",
                    "style": {
                        "navigationBarTitleText": "开始游戏",
                        "enablePullDownRefresh": false,
                        "navigationStyle": "custom",
                        "pageOrientation": "landscape"
                    }
                }
            ]
        },
        {
            "root": "pages/weightHeight",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/details",
                    "style": {
                        "navigationBarTitleText": "我的体重",
                        "navigationBarTextStyle": "white",
                        "navigationBarBackgroundColor": "#00be76"
                    }
                },
                {
                    "path": "user/record-list",
                    "style": {
                        "navigationBarTitleText": "体重记录"
                    }
                },
                {
                    "path": "user/ranking-list",
                    "style": {
                        "navigationBarTitleText": "排行榜"
                    }
                },
                {
                    "path": "clock-in/clock-in",
                    "style": {
                        "navigationBarTitleText": "打卡"
                    }
                }
            ]
        },
        {
            "root": "pages/puzzle",
            "pages": [
                {
                    "path": "puzzle-list",
                    "style": {
                        "navigationBarTitleText": "拼图任务"
                    }
                },
                {
                    "path": "admin/puzzle/add"
                },
                {
                    "path": "user/in-puzzle",
                    "style": {
                        "navigationBarBackgroundColor": "#B7C9FB",
                        "navigationBarTextStyle": "white",
                        "disableScroll": true
                    }
                }
            ]
        },
        {
            "root": "pages/form-submit",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/details"
                },
                {
                    "path": "user/submit-success",
                    "style": {
                        "navigationBarTitleText": "提交成功"
                    }
                },
                {
                    "path": "submit-record",
                    "style": {
                        "navigationBarTitleText": "提交记录",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/sports-center",
            "pages": [
                {
                    "path": "sports-data",
                    "style": {
                        "navigationBarTitleText": "统计"
                    }
                },
                {
                    "path": "add-record",
                    "style": {
                        "navigationBarTitleText": "添加"
                    }
                },
                {
                    "path": "record-list",
                    "style": {
                        "navigationBarTitleText": "记录",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "pages/item-storage",
            "pages": [
                {
                    "path": "space/list",
                    "style": {
                        "navigationBarTitleText": "储物位置"
                    }
                },
                {
                    "path": "space/add",
                    "style": {
                        "navigationBarTitleText": "添加位置"
                    }
                },
                {
                    "path": "item/list",
                    "style": {
                        "navigationBarTitleText": "物品",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "item/add/add"
                },
                {
                    "path": "item/add/batch",
                    "style": {
                        "navigationBarTitleText": "批量录入"
                    }
                },
                {
                    "path": "item/add/ai-chat",
                    "style": {
                        "navigationBarTitleText": "对话录入"
                    }
                },
                {
                    "path": "item/add/excel-import",
                    "style": {
                        "navigationBarTitleText": "表格导入"
                    }
                },
                {
                    "path": "item/details",
                    "style": {
                        "navigationBarTitleText": "物品详情"
                    }
                },
                {
                    "path": "ai-chat/query",
                    "style": {
                        "navigationBarTitleText": "物品位置查询"
                    }
                }
            ]
        },
        {
            "root": "pages/light-up-map",
            "pages": [
                {
                    "path": "admin/add"
                },
                {
                    "path": "admin/manage",
                    "style": {
                        "navigationBarTitleText": "活动管理"
                    }
                },
                {
                    "path": "user/details",
                    "style": {
                        "navigationBarTitleText": "活动详情"
                    }
                },
                {
                    "path": "user/light-city-list",
                    "style": {
                        "navigationBarTitleText": "足记"
                    }
                }
            ]
        },
        {
            "root": "pages/take-oath",
            "pages": [
                {
                    "path": "list",
                    "style": {
                        "navigationBarTitleText": "宣誓词",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "submit/submit",
                    "style": {
                        "navigationBarTitleText": "提交宣誓词"
                    }
                },
                {
                    "path": "submit/edit-image",
                    "style": {
                        "navigationBarTitleText": "合成照片",
                        "navigationBarBackgroundColor": "#000000",
                        "navigationBarTextStyle": "white",
                        "disableScroll": true
                    }
                },
                {
                    "path": "submit/record",
                    "style": {
                        "navigationBarTitleText": "录制"
                    }
                },
                {
                    "path": "details",
                    "style": {
                        "navigationBarTitleText": "宣誓词"
                    }
                }
            ]
        },
        {
            "root": "pages/health-management",
            "pages": [
                {
                    "path": "measure-record",
                    "style": {
                        "navigationBarTitleText": "测量记录",
                        "navigationBarBackgroundColor": "#F2F7FA"
                    }
                },
                {
                    "path": "statistics-7-day",
                    "style": {
                        "navigationBarTitleText": "分析",
                        "navigationBarBackgroundColor": "#F2F7FA"
                    }
                },
                {
                    "path": "family/list",
                    "style": {
                        "navigationBarTitleText": "家庭成员管理",
                        "navigationBarBackgroundColor": "#F2F7FA"
                    }
                },
                {
                    "path": "family/add"
                }
            ]
        },
        {
            "root": "pages/historical-event-sort",
            "pages": [
                {
                    "path": "admin/list",
                    "style": {
                        "navigationBarTitleText": "事件管理"
                    }
                },
                {
                    "path": "admin/level-event-set"
                },
                {
                    "path": "admin/add"
                },
                {
                    "path": "user/sorting",
                    "style": {
                        "navigationBarBackgroundColor": "#FFFCFD"
                    }
                },
                {
                    "path": "user/result",
                    "style": {
                        "navigationBarBackgroundColor": "#FFFCFD"
                    }
                }
            ]
        },
        {
            "root": "pages/fill-words",
            "pages": [
                {
                    "path": "fill-words",
                    "style": {
                        "navigationBarTitleText": "填词",
                        "navigationBarBackgroundColor": "#DA0900",
                        "navigationBarTextStyle": "white"
                    }
                },
                {
                    "path": "fill-couplet",
                    "style": {
                        "navigationBarTitleText": "猜对联",
                        "navigationBarBackgroundColor": "#DA0900",
                        "navigationBarTextStyle": "white"
                    }
                }
            ]
        },
        {
            "root": "pages/red-small-games",
            "pages": [
                {
                    "path": "mine-sweeper/game",
                    "style": {
                        "navigationBarTitleText": "扫雷",
                        "navigationBarBackgroundColor": "#F2ECC1"
                    }
                },
                {
                    "path": "lian-lian-kan/game",
                    "style": {
                        "navigationBarTitleText": "连连看",
                        "navigationBarBackgroundColor": "#F2ECC1"
                    }
                },
                {
                    "path": "grab-grain/game",
                    "style": {
                        "navigationBarTitleText": "抢粮食",
                        "navigationBarBackgroundColor": "#F3CE88",
                        "disableScroll": true
                    }
                }
            ]
        },
        {
            "root": "pages/likou_dati",
            "pages": [
                {
                    "path": "pages/exam/exam_list/exam_list",
                    "style": {
                        "navigationBarTitleText": "我的考卷"
                    }
                },
                {
                    "path": "pages/exam/exam_question_add/exam_question_add"
                },
                {
                    "path": "pages/exam/exam_add/exam_add"
                },
                {
                    "path": "pages/user/image_house/image_house"
                },
                {
                    "path": "pages/exam/exam_details/exam_details"
                },
                {
                    "path": "pages/answer/answer_question/answer_question"
                },
                {
                    "path": "pages/answer/answer_result/answer_result"
                },
                {
                    "path": "pages/answer/answer_explain/answer_explain"
                },
                {
                    "path": "pages/answer/ranking_list/ranking_list"
                },
                {
                    "path": "pages/question/question_bank_list/question_bank_list"
                },
                {
                    "path": "pages/question/add_question/add_question"
                },
                {
                    "path": "pages/question/random_question_types/random_question_types"
                },
                {
                    "path": "pages/question/category_list/category_list"
                },
                {
                    "path": "pages/question/batch_import_question/excel_import/excel_import"
                },
                {
                    "path": "pages/question/batch_import_question/explain/explain"
                },
                {
                    "path": "pages/question/batch_import_question/preview_import/preview_import"
                },
                {
                    "path": "pages/question/batch_import_question/text_import/text_import"
                },
                {
                    "path": "pages/exam/export-questionnaire",
                    "style": {
                        "navigationBarTitleText": "导出问卷调查"
                    }
                },
                {
                    "path": "pages/answer/ranking_list/export_word"
                }
            ]
        }
    ],
    "preloadRule": {
        "pages/index/index": {
            "network": "all",
            "packages": [
                "pages/diy-page",
                "pages/user",
                "pages/activity"
            ]
        }
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#ffffff",
        "backgroundColor": "#f8f8f8",
        "h5": {
            "navigationStyle": "custom"
        }
    }
}
