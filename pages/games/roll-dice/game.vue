<template>
    <!-- 游戏主容器 -->
    <view
        class="dice-game-container"
        :style="{ backgroundImage: `url(${images.bgImg})` }"
    >
        <!-- 游戏主体区域 -->
        <main class="dice-game-main">
            <!-- 骰子碗区域 -->
            <section class="dice-bowl-section">
                <!-- 碗背景图片 -->
                <image
                    class="dice-bowl-image"
                    :src="images.bowl"
                    mode="widthFix"
                />

                <!-- 骰子动画容器组 -->
                <div class="dice-animation-group">
                    <!-- 单个骰子动画容器 -->
                    <div
                        v-for="(diceItem, diceIndex) in diceList"
                        :key="diceIndex"
                        class="dice-animation-container"
                        :style="{
                            left: `${diceItem.left}%`,
                            top: `${diceItem.top}%`
                        }"
                    >
                        <!-- 骰子物理动画元素 -->
                        <div
                            class="dice-physics-element"
                            :style="{
                                transform: diceItem.physics.showResult
                                    ? 'none'
                                    : `translate3d(${diceItem.physics.x}px, ${diceItem.physics.y}px, 0) rotate(${diceItem.physics.rotation}deg)`
                            }"
                        >
                            <!-- 最终结果显示 -->
                            <image
                                v-if="diceItem.physics.showResult"
                                class="dice-result-image"
                                :src="images[`dice${diceItem.value}`]"
                                mode="aspectFill"
                            />

                            <!-- 滚动动画显示 -->
                            <div
                                v-else
                                class="dice-rolling-container"
                            >
                                <image
                                    v-for="(diceImage, imageIndex) in diceImageList"
                                    :key="imageIndex"
                                    class="dice-rolling-image"
                                    :class="{
                                        'dice-rolling-image--active': imageIndex === diceItem.physics.randomDice
                                    }"
                                    :src="diceImage"
                                    mode="aspectFill"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 操作按钮区域 -->
            <section class="dice-controls-section">
                <!-- 掷骰子按钮 -->
                <button
                    class="dice-roll-button"
                    :class="{ 'dice-roll-button--disabled': isRolling }"
                    @click="rollDice"
                >
                    <!-- 按钮背景图片 -->
                    <image
                        class="dice-roll-button__background"
                        :src="images.rollBtnImg"
                        mode="widthFix"
                    />
                    <!-- 按钮文字 -->
                    <span class="dice-roll-button__text">掷骰子</span>
                </button>
            </section>
        </main>

        <!-- 游戏提示弹窗 -->
        <game-tips-popup
            ref="gameTipsPopup"
            :tips-list="tipsList"
            :show-ad="showAD"
            @startGame="startGame"
        />
    </view>
</template>

<script>
import {config} from './config'
import gameTipsPopup from '../components/game-tips-popup.vue'
import readyCountdown from '../components/ready-countdown.vue'

export default {
    components: {gameTipsPopup, readyCountdown},
    data() {
        return {
            images: config.images,
            diceImageList: [],

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            totalScore: 0, // 获取的总积分

            diceList: this.initDiceList(), // 骰子列表
            isRolling: false, // 是否正在投掷

            // 物理引擎参数
            physicsConfig: {
                gravity: 0.3,
                friction: 0.98,
                bounce: 0.7,
                bowlRadius: 40,
                diceSize: 15,
                animationDuration: 3000,
            },

            per_integral: 0,
            unit: '积分',
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                '🎲 同时掷出六枚神奇骰子',
                `🎯 骰子停止滚动后计算点数总和，每一点可获得${this.per_integral}${this.unit}`
            ]
        }
    },

    onLoad() {
        this.init()
    },

    methods: {
        async init() {
            const {dice1, dice2, dice3, dice4, dice5, dice6} = config.images
            this.diceImageList = [dice1, dice2, dice3, dice4, dice5, dice6]

            this.$refs.gameTipsPopup.open()
        },

        // 初始化骰子列表
        initDiceList() {
            return config.diceList.map((dice, index) => ({
                ...dice,
                physics: {
                    ...this.createDicePhysics(),
                    randomDice: index,
                },
            }))
        },

        // 创建骰子物理属性
        createDicePhysics() {
            return {
                active: false,
                x: 0,
                y: 0,
                vx: 0,
                vy: 0,
                rotation: 0,
                rotationSpeed: 0,
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: 0,
            }
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.gameStatus = 1
            this.totalScore = 0

            // 重置所有骰子的状态
            // this.diceList = this.initDiceList()
        },

        // 掷骰子
        rollDice() {
            if (this.isRolling) return
            this.isRolling = true

            // 为每个骰子生成随机点数
            this.diceList.forEach((dice, index) => {
                // 延迟启动每个骰子的物理动画
                setTimeout(() => {
                    this.startDicePhysicsAnimation(dice, index)
                    dice.value = Math.ceil(Math.random() * 6)
                    this.totalScore += dice.value
                }, index * 100)
            })

            // 等待所有动画完成后结束游戏
            setTimeout(() => {
                this.isRolling = false
                this.endGame()
            }, this.physicsConfig.animationDuration + 2000)
        },

        // 开始骰子物理动画
        startDicePhysicsAnimation(dice, index) {
            // 设置随机初始速度和旋转
            dice.physics = {
                active: true,
                x: (Math.random() - 0.5) * 30, // 随机初始X位置
                y: -40, // 从上方开始
                vx: (Math.random() - 0.5) * 6, // 随机水平速度
                vy: Math.random() * 2 + 1, // 随机垂直速度
                rotation: Math.random() * 360, // 随机初始旋转
                rotationSpeed: (Math.random() - 0.5) * 15, // 随机旋转速度
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: dice.physics.randomDice,
            }

            // 开始物理模拟
            this.runPhysicsSimulation(dice)
        },

        // 运行物理模拟
        runPhysicsSimulation(dice) {
            const startTime = Date.now()
            const {physicsConfig} = this

            const animate = () => {
                const elapsed = Date.now() - startTime

                if (elapsed > physicsConfig.animationDuration || dice.physics.settled) {
                    // 动画结束，显示最终结果
                    dice.physics.showResult = true

                    setTimeout(() => {
                        dice.physics.active = false
                    }, 500)
                    return
                }

                // 更新物理状态
                this.updateDicePhysics(dice.physics)

                // 继续动画
                setTimeout(() => animate(), 16)
            }

            animate()
        },

        // 更新骰子物理状态
        updateDicePhysics(physics) {
            const {physicsConfig} = this

            // 应用重力
            physics.vy += physicsConfig.gravity

            // 更新位置
            physics.x += physics.vx
            physics.y += physics.vy

            // 更新旋转
            physics.rotation += physics.rotationSpeed

            // 碗边界碰撞检测（圆形边界）
            const distanceFromCenter = Math.sqrt(physics.x * physics.x + physics.y * physics.y)

            if (distanceFromCenter > physicsConfig.bowlRadius - physicsConfig.diceSize) {
                // 碰撞到碗壁
                const angle = Math.atan2(physics.y, physics.x)
                const normalX = Math.cos(angle)
                const normalY = Math.sin(angle)

                // 反射速度
                const dotProduct = physics.vx * normalX + physics.vy * normalY
                physics.vx -= 2 * dotProduct * normalX * physicsConfig.bounce
                physics.vy -= 2 * dotProduct * normalY * physicsConfig.bounce

                // 调整位置到边界内
                const targetDistance = physicsConfig.bowlRadius - physicsConfig.diceSize
                physics.x = normalX * targetDistance
                physics.y = normalY * targetDistance

                // 增加旋转速度
                physics.rotationSpeed += (Math.random() - 0.5) * 8
            }

            // 底部碰撞
            if (physics.y > 15) {
                physics.y = 15
                physics.vy *= -physicsConfig.bounce
                physics.rotationSpeed *= 0.8
            }

            // 应用摩擦力
            physics.vx *= physicsConfig.friction
            physics.vy *= physicsConfig.friction
            physics.rotationSpeed *= physicsConfig.friction

            // 检查是否静止
            const speed = Math.sqrt(physics.vx * physics.vx + physics.vy * physics.vy)
            if (speed < 0.1 && Math.abs(physics.rotationSpeed) < 1 && physics.y > 10) {
                physics.settled = true
                physics.vx = 0
                physics.vy = 0
                physics.rotationSpeed = 0
                physics.showResult = true
            }

            physics.randomDice = Math.floor(Math.random() * 6)
        },

        endGame() {
            this.gameStatus = 2
        }
    },
}
</script>

<style scoped lang="scss">
/* ================================
   游戏主容器样式
   ================================ */
.dice-game-container {
    width: 100%;
    min-height: 100vh;
    padding-top: 10vh;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* ================================
   游戏主体区域样式
   ================================ */
.dice-game-main {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

/* ================================
   骰子碗区域样式
   ================================ */
.dice-bowl-section {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
}

.dice-bowl-image {
    width: 100%;
    height: auto;
    display: block;
}

/* ================================
   骰子动画相关样式
   ================================ */
.dice-animation-group {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.dice-animation-container {
    position: absolute;
    width: 10vw;
    max-width: 64px;
    height: 10vw;
    max-height: 64px;
    pointer-events: none;
}

.dice-physics-element {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: center;
    transition: none;
}

.dice-result-image {
    width: 100%;
    height: 100%;
    display: block;
}

.dice-rolling-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.dice-rolling-image {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.1s ease;

    &--active {
        opacity: 1;
    }
}

/* ================================
   操作按钮区域样式
   ================================ */
.dice-controls-section {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.dice-roll-button {
    position: relative;
    width: 40%;
    border: none;
    background: transparent;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;

    &:active {
        transform: scale(0.95);
        box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.2);
    }

    &--disabled {
        opacity: 0.6;
        pointer-events: none;
        cursor: not-allowed;
    }
}

.dice-roll-button__background {
    width: 100%;
    height: auto;
    display: block;
}

.dice-roll-button__text {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 2px;
    text-align: center;
    white-space: nowrap;
    pointer-events: none;
}
</style>
