<template>
    <view
        class="content relative f-j-a-c"
        style="padding-top: 10vh"
        :style="{ backgroundImage: `url(${images.bgImg})` }"
    >
        <!-- 游戏区域 -->
        <view class="game-area">
            <!-- 碗容器 -->
            <view class="width100 radius_5 f-j-a-c relative">
                <image :src="images.bowl" mode="widthFix"/>

                <!-- 物理动画骰子 -->
                <view
                    v-for="(item, index) in diceList"
                    :key="index"
                    class="dice-physics-container"
                    :style="{ left: `${item.left}%`, top: `${item.top}%` }"
                >
                    <view
                        class="physics-dice"
                        :style="{transform: item.physics.showResult ? 'none' : `translate3d(${item.physics.x}px, ${item.physics.y}px, 0) rotate(${item.physics.rotation}deg)`}"
                    >
                        <image
                            v-if="item.physics.showResult"
                            class="dice-image"
                            :src="images[`dice${item.value}`]"
                            mode="aspectFill"
                        />
                        <div v-else class="relative width100 height100">
                            <image
                                class="absolute"
                                style="left: 0; top: 0"
                                :style="{ opacity: idx == item.physics.randomDice ? 1 : 0 }"
                                v-for="(itm, idx) in diceImageList"
                                :key="idx"
                                :src="itm"
                                mode="aspectFill"
                            />
                        </div>
                    </view>
                </view>
            </view>

            <!-- 掷骰子按钮 -->
            <view
                class="roll-btn f relative width40 mt2"
                :class="{ disabled: isRolling }"
                @click="rollDice"
            >
                <image :src="images.rollBtnImg" mode="widthFix"/>
                <view class="text f-j-a-c colorfff">掷骰子</view>
            </view>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>
    </view>
</template>

<script>
import {config} from './config'
import gameTipsPopup from '../components/game-tips-popup.vue'
import readyCountdown from '../components/ready-countdown.vue'

export default {
    components: {gameTipsPopup, readyCountdown},
    data() {
        return {
            images: config.images,
            diceImageList: [],

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            totalScore: 0, // 获取的总积分

            diceList: this.initDiceList(), // 骰子列表
            isRolling: false, // 是否正在投掷

            // 物理引擎参数
            physicsConfig: {
                gravity: 0.3,
                friction: 0.98,
                bounce: 0.7,
                bowlRadius: 40,
                diceSize: 15,
                animationDuration: 3000,
            },

            per_integral: 0,
            unit: '积分',
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                '🎲 同时掷出六枚神奇骰子',
                `🎯 骰子停止滚动后计算点数总和，每一点可获得${this.per_integral}${this.unit}`
            ]
        }
    },

    onLoad() {
        this.init()
    },

    methods: {
        async init() {
            const {dice1, dice2, dice3, dice4, dice5, dice6} = config.images
            this.diceImageList = [dice1, dice2, dice3, dice4, dice5, dice6]

            this.$refs.gameTipsPopup.open()
        },

        // 初始化骰子列表
        initDiceList() {
            return config.diceList.map((dice, index) => ({
                ...dice,
                physics: {
                    ...this.createDicePhysics(),
                    randomDice: index,
                },
            }))
        },

        // 创建骰子物理属性
        createDicePhysics() {
            return {
                active: false,
                x: 0,
                y: 0,
                vx: 0,
                vy: 0,
                rotation: 0,
                rotationSpeed: 0,
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: 0,
            }
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.gameStatus = 1
            this.totalScore = 0

            // 重置所有骰子的状态
            // this.diceList = this.initDiceList()
        },

        // 掷骰子
        rollDice() {
            if (this.isRolling) return
            this.isRolling = true

            // 为每个骰子生成随机点数
            this.diceList.forEach((dice, index) => {
                // 延迟启动每个骰子的物理动画
                setTimeout(() => {
                    this.startDicePhysicsAnimation(dice, index)
                    dice.value = Math.ceil(Math.random() * 6)
                    this.totalScore += dice.value
                }, index * 100)
            })

            // 等待所有动画完成后结束游戏
            setTimeout(() => {
                this.isRolling = false
                this.endGame()
            }, this.physicsConfig.animationDuration + 2000)
        },

        // 开始骰子物理动画
        startDicePhysicsAnimation(dice, index) {
            // 设置随机初始速度和旋转
            dice.physics = {
                active: true,
                x: (Math.random() - 0.5) * 30, // 随机初始X位置
                y: -40, // 从上方开始
                vx: (Math.random() - 0.5) * 6, // 随机水平速度
                vy: Math.random() * 2 + 1, // 随机垂直速度
                rotation: Math.random() * 360, // 随机初始旋转
                rotationSpeed: (Math.random() - 0.5) * 15, // 随机旋转速度
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: dice.physics.randomDice,
            }

            // 开始物理模拟
            this.runPhysicsSimulation(dice)
        },

        // 运行物理模拟
        runPhysicsSimulation(dice) {
            const startTime = Date.now()
            const {physicsConfig} = this

            const animate = () => {
                const elapsed = Date.now() - startTime

                if (elapsed > physicsConfig.animationDuration || dice.physics.settled) {
                    // 动画结束，显示最终结果
                    dice.physics.showResult = true

                    setTimeout(() => {
                        dice.physics.active = false
                    }, 500)
                    return
                }

                // 更新物理状态
                this.updateDicePhysics(dice.physics)

                // 继续动画
                setTimeout(() => animate(), 16)
            }

            animate()
        },

        // 更新骰子物理状态
        updateDicePhysics(physics) {
            const {physicsConfig} = this

            // 应用重力
            physics.vy += physicsConfig.gravity

            // 更新位置
            physics.x += physics.vx
            physics.y += physics.vy

            // 更新旋转
            physics.rotation += physics.rotationSpeed

            // 碗边界碰撞检测（圆形边界）
            const distanceFromCenter = Math.sqrt(physics.x * physics.x + physics.y * physics.y)

            if (distanceFromCenter > physicsConfig.bowlRadius - physicsConfig.diceSize) {
                // 碰撞到碗壁
                const angle = Math.atan2(physics.y, physics.x)
                const normalX = Math.cos(angle)
                const normalY = Math.sin(angle)

                // 反射速度
                const dotProduct = physics.vx * normalX + physics.vy * normalY
                physics.vx -= 2 * dotProduct * normalX * physicsConfig.bounce
                physics.vy -= 2 * dotProduct * normalY * physicsConfig.bounce

                // 调整位置到边界内
                const targetDistance = physicsConfig.bowlRadius - physicsConfig.diceSize
                physics.x = normalX * targetDistance
                physics.y = normalY * targetDistance

                // 增加旋转速度
                physics.rotationSpeed += (Math.random() - 0.5) * 8
            }

            // 底部碰撞
            if (physics.y > 15) {
                physics.y = 15
                physics.vy *= -physicsConfig.bounce
                physics.rotationSpeed *= 0.8
            }

            // 应用摩擦力
            physics.vx *= physicsConfig.friction
            physics.vy *= physicsConfig.friction
            physics.rotationSpeed *= physicsConfig.friction

            // 检查是否静止
            const speed = Math.sqrt(physics.vx * physics.vx + physics.vy * physics.vy)
            if (speed < 0.1 && Math.abs(physics.rotationSpeed) < 1 && physics.y > 10) {
                physics.settled = true
                physics.vx = 0
                physics.vy = 0
                physics.rotationSpeed = 0
                physics.showResult = true
            }

            physics.randomDice = Math.floor(Math.random() * 6)
        },

        endGame() {
            this.gameStatus = 2
        }
    },
}
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    min-height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
    overflow: hidden;
}

.game-area {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
        width: 100%;
        height: auto;
    }
}

.roll-btn {
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
        box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.2);
    }

    &.disabled {
        opacity: 0.6;
        pointer-events: none;
    }

    .text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        letter-spacing: 2px;
        font-weight: 600;
    }
}

// 骰子物理动画容器
.dice-physics-container {
    position: absolute;
    width: 10vw;
    max-width: 64px;
    height: 10vw;
    max-height: 64px;
    pointer-events: none;
}

.physics-dice {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: center;
    transition: none;

    image {
        width: 100%;
        height: 100%;
    }
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.width100 {
    width: 100%;
}

.f {
    display: flex;
}

.width40 {
    width: 40%;
}

.mt2 {
    margin-top: 2rem !important;
}

.height100 {
    height: 100%;
}

.radius_5 {
    border-radius: .5rem;
}

.colorfff {
    color: #fff;
}
</style>
