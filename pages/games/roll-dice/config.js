/*
 * @Author: chliangck <EMAIL>
 * @LastEditors: chliangck <EMAIL>
 * @LastEditTime: 2025-08-14 14:42:40
 * @FilePath: /线上活动王/pages/game/roll-dice/config.js
 * @Description: 掷骰子游戏配置文件
 */

const baseUrl =
  'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/roll-dice/'

export const config = {
  images: {
    // 背景图片
    bgImg: `${baseUrl}bg.png`,
    bowl: `${baseUrl}bowl.png`,
    rollBtnImg: `${baseUrl}button.png`,

    // 骰子相关图片
    dice1: `${baseUrl}dice/1.png`,
    dice2: `${baseUrl}dice/2.png`,
    dice3: `${baseUrl}dice/3.png`,
    dice4: `${baseUrl}dice/4.png`,
    dice5: `${baseUrl}dice/5.png`,
    dice6: `${baseUrl}dice/6.png`,
  },

  /**
   * @description: 骰子列表
   * @param value 获得骰子点数
   * @param rolling 骰子是否正在滚动
   * @return {*}
   */
  diceList: [
    {
      value: 0,
      rolling: false,
      left: 25,
      top: 40,
    },
    {
      value: 0,
      rolling: false,
      left: 35,
      top: 40,
    },
    {
      value: 0,
      rolling: false,
      left: 45,
      top: 40,
    },
    {
      value: 0,
      rolling: false,
      left: 25,
      top: 60,
    },
    {
      value: 0,
      rolling: false,
      left: 35,
      top: 60,
    },
    {
      value: 0,
      rolling: false,
      left: 45,
      top: 60,
    },
  ],
}
