<template>
    <view class="page bg-background">
        <view class="top-container">
            <view class="search-container bg-white flex-kai">
                <view class="search-bar flex-row">
                    <view class="search-icon flex-all-center">
                        <uni-icons type="search" size="18" color="#bbbec4"/>
                    </view>
                    <input type="text" class="search-input" v-model="keyword" confirm-type="search"
                           placeholder="搜索物品名称、备注" @confirm="search"/>
                    <view class="search-button bg-light-primary color-white text-center"
                          hover-class="navigator-hover" @click="search">搜索</view>
                </view>
                <view class="ai-chat-icon flex-all-center" hover-class="navigator-hover"
                      @click="toAiChatQuery">
                    <text class="iconfont icon-ai-chat"></text>
                </view>
            </view>

            <view class="clear clearfix bg-white">

                <scroll-view v-if="category_list.length" class="category-bar fl" :scroll-x="true"
                             :scroll-left="categoryListScrollLeft" :scroll-with-animation="true">
                    <view class="flex-row">
                        <view class="category-item font14"
                              :class="category_id === 0 ? 'color-light-primary' : 'color-content'"
                              @click="categoryChange(0)">全部
                            <view v-show="category_id === 0" class="category-active"></view>
                        </view>
                        <view class="category-item font14"
                              :class="item.category_id === category_id ? 'color-light-primary' : 'color-content'"
                              v-for="item in category_list" :key="item.category_id"
                              @click="categoryChange(item.category_id)">
                            {{ item.name }}

                            <view v-show="item.category_id === category_id" class="category-active"></view>
                        </view>
                    </view>
                </scroll-view>
                <view class="fr">
                    <view class="category-more-icon" @click="$refs.categoryChoosePopup.open()">
                        <text class="font14 color-sub">更多</text>
                        <uni-icons type="forward" size="14" color="#80848f"/>
                    </view>
                </view>
            </view>

            <view class="clear clearfix bg-white">
                <view class="fl pl10 pr10 flex-row" style="line-height: 40px;">
                    <view class="flex-row color-sub font14" @click="$refs.spaceChoose.open()">
                        <view class="pr5">存放位置:</view>
                        <view style="width: calc(100vw - 240px); overflow-x: auto;">
                            <space-path v-if="space_id" :space-id="space_id" :wrap="false"/>
                            <text v-else>不限</text>
                        </view>
                    </view>
                    <view v-if="space_id" class="flex-all-center pl5" @click="spaceChange(null)">
                        <uni-icons type="clear" size="18" color="#bbbec4"/>
                    </view>
                </view>
                
                <view class="fr flex-row">
                    <view class="show-type-switch flex-all-center" @click="exportItemList">
                        <text class="iconfont icon-export-excel font20 color-sub"></text>
                    </view>
                    
                    <view class="show-type-switch flex-all-center" @click="$refs.screenPopup.open()">
                        <text class="iconfont icon-screening font20"
                              :class="haveScreen ? 'color-light-primary' : 'color-sub'"></text>
                    </view>
                    
                    <view v-show="!is_copy && item_list.length">
                        <view v-for="item in ['list', 'grid']" :key="item" v-show="item === itemListShowType"
                              class="show-type-switch flex-all-center"
                              @click="itemListShowType = itemListShowType === 'list' ? 'grid' : 'list'">
                            <uni-icons v-if="itemListShowType === 'grid'" type="list" size="22" color="#80848f"/>
                            <text v-else class="iconfont icon-grid font20 color-sub"></text>
                        </view>
                    </view>
                </view>
            </view>
            
            
            <view v-if="is_copy" class="text-center bg-background font14"
                  style="height: 40px; line-height: 40px; color: #ff7e92;">
                请点击需要复制物品的"复制物品"按钮
            </view>
        </view>
        
        <view v-if="is_copy" style="height: 40px;"></view>

        <screen-popup ref="screenPopup" :date-types.sync="date_types" :begin-time.sync="begin_time"
                      :end-time.sync="end_time" @confirm="search"/>
        
        <view class="item-list">
            <item-list :list="item_list" :show-type="itemListShowType"
                       :show-space-path="itemListShowType === 'list'" @itemClick="lookItem"
                       :show-copy="is_copy" @copyItem="copyItem"/>
        </view>
        
        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="width: 100%; height: 20vh;"></view>
            <load-ani/>
        </view>

        <view v-if="!loading && !item_list.length" class="text-center"
              style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂未添加物品</view>
            <view class="add-button bg-light-primary color-white" hover-class="navigator-hover"
                  @click="toAddItem">添加物品
            </view>
        </view>

        <view v-if="!is_copy" class="tab-bar flex-row bg-white">
            <view class="tab-bar-item" hover-class="navigator-hover">
                <uni-icons type="list" color="#5cadff" size="34"/>
                <view class="tab-item-text font12 color-light-primary">物品</view>
            </view>
            <view class="tab-bar-item" hover-class="navigator-hover" @click="toAddItem">
                <uni-icons type="plus" color="#80848f" size="34"/>
                <view class="tab-item-text font12 color-sub">添加</view>
            </view>
            <view class="tab-bar-item" hover-class="navigator-hover" @click="toSpaceList">
                <uni-icons type="map" color="#80848f" size="34"/>
                <view class="tab-item-text font12 color-sub">位置</view>
            </view>
        </view>

        <category-choose-popup ref="categoryChoosePopup" :category-id="category_id"
                               @clickItem="categoryChooseConfirm" @uploadCategoryList="getCategoryList"/>

        <space-choose ref="spaceChoose" :pid="space_id" @confirm="spaceChange"/>

        <add-item-type-popup ref="addItemTypePopup" @itemListUpload="reloadItemList"/>
    </view>
</template>

<script>
import _API_ from '../api'
import openExcelFile from "@/utils/open-excel-file"
import itemList from "../components/item/item-list.vue"
import categoryChoosePopup from "../components/category-choose-popup.vue"
import spacePath from "../components/space-path.vue"
import spaceChoose from "../components/space-choose.vue"
import addItemTypePopup from "../components/add-item-type-popup.vue"
import screenPopup from "../components/screen-popup.vue"

export default {
    components: {spacePath, spaceChoose, itemList, categoryChoosePopup, addItemTypePopup, screenPopup},
    data() {
        return {
            loading: true,
            item_list: [],
            current_page: 1,
            is_last_page: false,
            itemListShowType: 'list',
            keyword: '',
            category_list: [],
            categoryListScrollLeft: 0,
            category_id: 0,
            space_id: null,
            date_types: 1,
            begin_time: '',
            end_time: '',
            is_copy: false
        }
    },
    
    computed: {
        // 有筛选条件，筛选icon图标需要高亮显示
        haveScreen() {
            const screenKeys = ['begin_time', 'end_time']
            return screenKeys.some(key => this[key])
        }  
    },
    

    onLoad(params) {
        if (params.copy) this.is_copy = true
        this.$login.uniLogin(() => {
            this.init()
        })
    },
    
    onPullDownRefresh() {
        if (this.loading) return
        this.reloadItemList().finally(() => uni.stopPullDownRefresh())
    },
    
    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.current_page++
        this.getItemList()
    },

    methods: {
        async init() {
            // 需要先获取一次无限级位置分类，不然让物品组件去获取的话，同时多个组件一起获取会有多条请求记录
            await _API_.getSpaceListNotLimit()
            await this.getCategoryList()
            await this.getItemList()
        },

        async getCategoryList() {
            let category_list = await _API_.getSystemCategory()
            const my = await _API_.getMyCategoryList()
            if (my.length) category_list = [...category_list, ...my]
            if (category_list.length) this.category_list = category_list

            // 主要是针对从本页面进入分类管理删掉了当前查看的分类，因为分类删掉了，所以不能查看，改为查看所有
            if (this.category_id && !my.some(c => c.category_id === this.category_id)) {
                this.category_id = 0
                await this.getItemList()
            }
        },

        categoryChooseConfirm(category) {
            this.categoryChange(category.category_id)
        },

        categoryChange(category_id) {
            if (this.loading || category_id === this.category_id) return
            this.category_id = category_id

            // 设置scrollLeft让当前item居中
            const query = uni.createSelectorQuery().in(this)
            query.selectAll('.category-item').boundingClientRect()
            query.select('.category-bar').boundingClientRect()
            query.exec(res => {
                // 因为“全部”是在页面结构单独写的，不在this.category_list里面，所以下标要加上“全部”的1
                const current = this.category_list.findIndex(v => v.category_id === category_id) + 1
                
                const [items, {width: containerWidth}] = res
                const item = items[current]
                const itemOffsetLeft = items.slice(0, current).reduce((total, curr) => total + curr.width, 0)

                this.categoryListScrollLeft = itemOffsetLeft + item.width / 2 - containerWidth / 2
            })
            
            this.search()
        },

        spaceChange(space_id) {
            if (space_id === this.space_id) return
            this.space_id = space_id || null
            this.search()
        },

        search() {
            this.reloadItemList()
        },

        async reloadItemList() {
            this.current_page = 1
            await this.getItemList()
        },

        async getItemList() {
            if (this.current_page === 1) {
                this.item_list = []
                this.is_last_page = false
            }
            
            const data = {
                page: this.current_page,
                perpage: 20,
                keyword: this.keyword
            }
            if (this.category_id) data.item_category_id = this.category_id
            if (this.space_id) data.position_category_id = this.space_id
            if (this.begin_time || this.end_time) {
                data.date_types	 = this.date_types
                data.begin_time	 = this.begin_time
                data.end_time	 = this.end_time
            }
            
            this.loading = true
            const res = await _API_.getItemList(data)
            this.loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const list = this.initItemListData(res_data.data || [])
            this.item_list = [...this.item_list, ...list]
            this.is_last_page = res_data.is_lastpage
        },

        initItemListData(list) {
            return _API_.initItemListData(list)
        },

        lookItem(item_id) {
            this.$uni.navigateTo(`../item/details?item_id=${item_id}`, {
                events: {
                    reloadItemList: () => this.reloadItemList()
                }
            })
        },

        toAddItem() {
            this.$refs.addItemTypePopup.open()
            
            /*this.$uni.navigateTo(`./add`, {
                events: {
                    itemListUpload: () => this.reloadItemList()
                }
            })*/
        },

        toSpaceList() {
            this.$uni.navigateTo('../space/list')
        },

        toAiChatQuery() {
            this.$uni.navigateTo('../ai-chat/query')
        },

        copyItem(item_id) {
            this.$uni.navigateTo(`./add/add?item_id=${item_id}&copy=1`, {
                events: {
                    itemListUpload: () => {
                        this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
                        // 这里不用回退页面，在添加页面已经回退2级了
                    }
                }
            })
        },

        async exportItemList() {
            this.$uni.showLoading('导出中...')
            const res = await _API_.getItemList({
                page: 1,
                perpage: 1000
            })
            const list = res?.data?.list?.data || []
            if (!list.length) {
                uni.hideLoading()
                return this.$uni.showToast('没有需要导出的物品')
            }

            const {tableData, tableName} = this.excelDataProcessing(list)
            openExcelFile.openDocument(tableData, tableName)
            uni.hideLoading()
        },

        excelDataProcessing(list) {
            const tableName = '物品列表'
            
            const keys = [
                {key: 'no', title: '序号'},
                {key: 'name', title: '物品名称'},
                {key: 'position_category_name', title: '存放位置'},
                {key: 'position_place', title: '位置说明'},
                {key: 'item_category_name', title: '物品分类'},
                {key: 'brand_name', title: '品牌名称'},
                {key: 'amount', title: '单价'},
                {key: 'all_amount', title: '总价'},
                {key: 'num', title: '数量'},
                {key: 'left_num', title: '剩余数量'},
                {key: 'buy_date', title: '购买日期'},
                {key: 'product_date', title: '生产日期'},
                {key: 'expired_date', title: '到期日期'},
                {key: 'open_date', title: '开封日期'},
                {key: 'memo', title: '备注说明'},
                {key: 'sort_num', title: '排序'},
                {key: 'logo', title: '物品图片地址'}
            ]
            
            const tHead = keys.map(item => item.title)

            const tBody = list.map((v, i) => {
                const item = []
                keys.forEach(key => {
                    switch (key.key) {
                        case 'no':
                            item.push(i + 1)
                            break
                        case 'position_category_name':
                            item.push(v.position_cate_details?.name || '')
                            break
                        case 'item_category_name':
                            item.push(v.item_cate_details?.name || '')
                            break
                        default:
                            item.push(v[key.key] || '')
                    }
                })
                return item
            })
            const tableData = [tHead, ...tBody]
            return {tableData, tableName}
        }
    },
    
    onShareAppMessage() {
        return {
            // “物品管理” => “物品列表”  阿雷口头提的 2024-09-04 17:20:04
            // title: '物品管理',
            title: '物品列表',
            path: '/pages/item-storage/item/list'
        }
    },
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-top: 146px;
    padding-bottom: 150px;
    box-sizing: border-box;
}

.top-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

.search-container {
    padding: 10px;

    $height: 40px;
    
    .search-bar {
        height: $height;
        padding: 2px;
        border: 1px solid #5cadff;
        border-radius: calc((#{$height} + 6px) / 2);
        width: 100%;

        .search-icon, .search-input, .search-button {
            height: $height;
            line-height: $height;
        }

        .search-icon {
            width: 30px;
            min-width: 30px;
        }

        .search-input {
            width: 100%;
        }

        .search-button {
            width: 80px;
            min-width: 80px;
            border-radius: calc(#{$height} / 2);
        }
    }

    .ai-chat-icon {
        width: $height;
        min-width: $height;
        margin-left: 10px;

        .iconfont {
            font-size: 40px;
            color: #94caff;
        }
    }
}

.category-bar {
    width: calc(100% - 70px);
    padding: 0 10px;
    box-sizing: border-box;
    
    .category-item {
        display: inline-block;
        white-space: nowrap;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
        box-sizing: border-box;
        position: relative;
        
        .category-active {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 16px;
            margin-left: -8px;
            height: 2px;
            background-color: #2d8cf0;
        }
    }
}

.category-more-icon {
    height: 40px;
    line-height: 34px;
    padding: 0 10px;
}

.show-type-switch {
    width: 40px;
    height: 40px;
}

.add-button {
    margin: 20px auto;
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}

.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    
    .tab-bar-item {
        width: calc(100% / 3);
        text-align: center;
        padding: 5px 0 10px;
        border-top: 1px solid #eee;

        .tab-item-text {
            position: relative;
            top: -5px;
        }
    }
}
</style>